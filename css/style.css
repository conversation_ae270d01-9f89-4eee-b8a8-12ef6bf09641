@import url('http://fonts.googleapis.com/css?family=Noto+Serif:400,400italic,700|Open+Sans:400,600,700'); 
@import url('font-awesome.css');  
@import url('animate.css');

body {
	font-family:'Open Sans', Arial, sans-serif;
	font-size:14px;
	font-weight:300;
	line-height:1.6em;
	color:#656565;
	/* background: #EFEFEF; */
}

a:active {
	outline:0;
}

.clear {
	clear:both;
}

h1,h2, h3, h4, h5, h6 {
	font-family:'Open Sans', Arial, sans-serif;
	font-weight: 600;
	line-height:1.1em;
	color:#333;
	margin-bottom: 20px;
}
 h2{
    font-size: 26px;
    font-weight: 700;
}
.container {
	padding:0 20px 0 20px;
	position:relative;
}
.help-block ul li {
    color: red;
}
.form-control {
    margin-bottom: 15px;
}
#wrapper{
	width:100%;
	margin:0;	
	padding:0;
}
.navbar-toggle {
    position: relative;
    float: right;
    padding: 9px 10px;
    margin-top: 8px;
    margin-right: 15px;
    margin-bottom: 8px;
    background-color: transparent;
    background-image: none;
    border: 0;
    border-radius: 4px;
}
.row,.row-fluid {
	margin-bottom:30px;
}

.row .row,.row-fluid .row-fluid{
	margin-bottom:30px;
}

.row.nomargin,.row-fluid.nomargin {
	margin-bottom:0;
}

img.img-polaroid {
	margin:0 0 20px 0;
}
.img-box {
	max-width:100%;
}
.flex-control-nav li{
display:none;
}
/*  Header
==================================== */
 
header .navbar {
    margin-bottom: 0;
}

.navbar-default {
    border: none;
}

.navbar-brand {
    color: #222;
	text-transform: uppercase;
    font-size: 24px;
    font-weight: 700;
    line-height: 1em;
	letter-spacing: -1px; 
    padding: 0 0 0 15px;
}
.navbar-default .navbar-brand{
color: #1891EC;
}
.navbar-default .navbar-brand img{
width:140px;
}
header .navbar-collapse  ul.navbar-nav {
    float: right;
    margin-right: 0;
}
header .navbar {min-height: 70px;padding: 18px 0;background: #ffffff;}
.home-page header .navbar-default{
    background: #fff;
    /* position: absolute; */
    width: 100%;
}

header .nav li a:hover,
header .nav li a:focus,
header .nav li.active a,
header .nav li.active a:hover,
header .nav li a.dropdown-toggle:hover,
header .nav li a.dropdown-toggle:focus,
header .nav li.active ul.dropdown-menu li a:hover,
header .nav li.active ul.dropdown-menu li.active a{
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}


header .navbar-default .navbar-nav > .open > a,
header .navbar-default .navbar-nav > .open > a:hover,
header .navbar-default .navbar-nav > .open > a:focus {
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}


header .navbar {
    min-height: 70px;
    padding: 11px 0;
}

header .navbar-nav > li  {
    padding-top: 5px;
}

header  .navbar-nav > li > a {
    padding-bottom: 6px;
    padding-top: 5px;
    margin-left: 2px;
    line-height: 30px;
	font-weight: 700;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}


.dropdown-menu li a:hover {
    color: #fff !important;
}

header .nav .caret {
    border-bottom-color: #f5f5f5;
    border-top-color: #f5f5f5;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  background-color: #fff;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  background-color:  #fff;
}	
	

.dropdown-menu  {
    box-shadow: none;
    border-radius: 0;
	border: none;
}

.dropdown-menu li:last-child  {
	padding-bottom: 0 !important;
	margin-bottom: 0;
}

header .nav li .dropdown-menu  {
   padding: 0;
}

header .nav li .dropdown-menu li a {
   line-height: 28px;
   padding: 3px 12px;
}

/*Client Slider*/
.clients{
	padding:35px 0;
}
.clients-control{
	    position: absolute;
    right: 20px;
    top: 5px;
}
.clients-slider .owl-item{margin:0 10px; display:inline-block;}
.clients-slider .item img{
	
display: block;
	
/* background-color:#fafafa; */
}
.clients-slider .item img.colored {
	top: 0;
	display: none;
	/* background-color:#f5f5f5; */
}
.clients-slider .item {
    margin: 1px;
}
.clients-slider .item:hover img.colored{display:block;}
.clients-slider .item:hover img{display:none;}
.clients-control .btn,
.clients-control .btn{
	margin-bottom:0px;
	margin-top: 17px;
	padding: 0px 6px;
	font-size: 11px;
}


/* --- menu --- */

header .navigation {
	float:right;
}

header ul.nav li {
	border:none;
	margin:0;
}

header ul.nav li a {	
	font-size:12px;
	border:none;
	font-weight:700;
	text-transform:uppercase;
}

header ul.nav li ul li a {	
	font-size:12px;
	border:none;
	font-weight:300;
	text-transform:uppercase;
}


.navbar .nav > li > a {
  color: #055999;
  text-shadow: none;
  border: 1px solid rgba(255, 255, 255, 0) !important;
  font-size: 14px;
  font-weight: normal;
}

.navbar .nav a:hover {
	background: #055999 !important;
	color: #ffffff !important;
}

.navbar .nav > .active > a,.navbar .nav > .active > a:hover {
	background:none;
	color: #ffffff !important;
	/* border: 1px solid #055999 !important; */
	background: #055999;
}

.navbar .nav > .active > a:active,.navbar .nav > .active > a:focus {
	background:none;
	outline:0;
}

.navbar .nav li .dropdown-menu {
	z-index:2000;
}

header ul.nav li ul {
	margin-top:1px;
}
header ul.nav li ul li ul {
	margin:1px 0 0 1px;
}
.dropdown-menu .dropdown i {
	position:absolute;
	right:0;
	margin-top:3px;
	padding-left:20px;
}

.navbar .nav > li > .dropdown-menu:before {
  display: inline-block;
  border-right: none;
  border-bottom: none;
  border-left: none;
  border-bottom-color: none;
  content:none;
}
.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:hover, .navbar-default .navbar-nav>.active>a:focus {color: #FFFFFF;/* border: 1px solid #FFFFFF !important; */background: #055999;color: #ffffff !important;}


ul.nav li.dropdown a {
	z-index:1000;
	display:block;
}

 select.selectmenu {
	display:none;
}
.pageTitle{
color: #fff;
margin: 30px 0 3px;
display: inline-block;
}
 
#banner{
	width: 100%;
	background:#000;
	position:relative;
	margin:0;
	padding:0;
	float: left;
	width: 100%;
}

/*  Sliders
==================================== */
/* --- flexslider --- */
#main-slider:before {/* content: ''; *//* width: 100%; *//* height: 100%; *//* background: rgba(0, 148, 255, 0.74); *//* z-index: 1; *//* position: absolute; */}
.flex-direction-nav a{
display:none;
}
.flexslider {
	padding:0;
	position: relative;
	zoom:1;
	background: #055999;
}
.flex-direction-nav .flex-prev{
left:0px; 
}
.flex-direction-nav .flex-next{ 
right:0px;
}
.flex-caption {zoom: 1;bottom: 0;background-color: transparent;color: #fff;margin: 0;padding: 25px 25px 25px 30px;position: absolute;left: 0;text-align: left;margin: 10px auto;right: 0px;display: inline-block;margin-left: 12%;}
.flex-caption h3 {color: #ffffff;margin-bottom: 15px;text-transform: uppercase;font-size: 38px;font-weight: bold;}
.flex-caption p {margin: 12px 0 56px;font-size: 20px;color: #ffffff;}
.skill-home{margin-bottom:50px;float: left;width: 100%;}
.c1{
/* border: #ed5441 1px solid; */
/* background:#ed5441; */
}
.c2{
/* border: #24DB5A 1px solid; */
/* background: #24DB5A; */
}
.c3{
/* border: #EC1890 1px solid; */
/* background: #EC1890; */
}
.c4{
/* border: #609cec 1px solid; */
/* background:#609cec; */
}
.skill-home .icons {padding: 36px 0 14px 0px;width: 100%;color: #797979;font-size: 42px;font-size: 68px;text-align: center;-ms-border-radius: 50%;-moz-border-radius: 50%;-webkit-border-radius: 50%;border-radius: 0;display: inline-table;float: left;}
.skill-home h2 {
padding-top: 20px;
font-size: 36px;
font-weight: 700;
} 
.skill-home h3 { 
font-size: 20px;
font-weight: 600;
}
.skill-home a {color: #ffffff;text-decoration: none;font-size: 13px;background: #055999;padding: 7px 20px;margin-top: 10px;display: inline-block;border: 1px solid #055999;}
.skill-home .box:hover{
	background: #ffffff;
	/* cursor:pointer; */
	/* color: #fff; */
	border: 1px solid #055999;
}
.skill-home .box:hover .icons,
.skill-home .box:hover h3, .skill-home .box:hover a {
	/* color:#fff; */
}
.skill-home a:hover {
    background: transparent;
    color: #02477b;
    border: 1px solid #055999;
}
.testimonial-solid {
padding: 50px 0 60px 0;
margin: 0 0 0 0;
background: #FFFFFF;
text-align: center;
}
.testi-icon-area {
text-align: center;
position: absolute;
top: -84px;
margin: 0 auto; 
width: 100%;
}
.testi-icon-area .quote {
padding: 15px 0 0 0;
margin: 0 0 0 0;
background: #ffffff;
text-align: center;
color: #1891EC;
display: inline-table;
width: 70px;
height: 70px;
-ms-border-radius: 50%;
-moz-border-radius: 50%;
-webkit-border-radius: 50%;
border-radius: 50%;
font-size: 42px; 
border: 1px solid #1891EC;
display: none;
}

.testi-icon-area .carousel-inner { 
margin: 20px 0;
}
.carousel-indicators {
bottom: -30px;
}
.text-center img {
margin: auto;
}
.aboutUs{padding: 80px 0 0;background: #055999;color: #fff;}
img.img-center {
margin: 0 auto;
display: block;
max-width: 100%;
}
.aboutUs h2 {
    color: #fff;
}
/* Testimonial
----------------------------------*/
.testimonial-area {
padding: 0 0 0 0;
margin:0;
background: url(../img/low-poly01.jpg) fixed center center;
background-size: cover;
-webkit-background-size: cover;
-moz-background-size: cover;
-ms-background-size: cover;
}
.testimonial-solid p {
color: #000000;
font-size: 16px;
line-height: 30px;
font-style: italic;
} 
section.jumbobox {
	background:#fff;
	padding: 28px 0 0 0;
	float: left;
	width: 100%;
	text-align: center;
}
.team-member{
	text-align:center;
}
.team-member h4{
	padding:10px 0 0;
	text-align:center;
	margin:0;
}
/* Clients
------------------------------------ */
#clients {
  padding: 67px 0; }
  #clients .client .img {
    height: 76px;
    width: 138px;
    cursor: pointer;
    -webkit-transition: box-shadow .1s linear;
    -moz-transition: box-shadow .1s linear;
    transition: box-shadow .1s linear; }
    #clients .client .img:hover {
      cursor: pointer;
      /*box-shadow: 0px 0px 2px 0px rgb(155, 155, 155);*/
      border-radius: 8px; }
  #clients .client .client1 {
    background: url("../img/client1.png") 0 -75px; }
    #clients .client .client1:hover {
      background-position: 1px 0px; }
  #clients .client .client2 {
    background: url("../img/client2.png") 0 -75px; }
    #clients .client .client2:hover {
      background-position: -1px 0px; }
  #clients .client .client3 {
    background: url("../img/client3.png") 0 -76px; }
    #clients .client .client3:hover {
      background-position: 0px 0px; }


/* Content
==================================== */

#content {
	position:relative;
	/* background:#fff; */
	padding: 30px 0 0px 0;
}

#content img {
	max-width:100%;
	height:auto;
}
 
.cta-text {
	text-align: center;
	margin-top:10px;
}


.big-cta .cta {
	margin-top:10px;
}
 
.box {
	width: 100%;
	/* border: 1px solid #D4D4D4; */
	display: inline-block;
	background: #efefef;
	padding-bottom: 20px;
	border: 1px solid #efefef;
}
.box-gray  {
	background: #f8f8f8;
	padding: 20px 20px 30px;
}
.box-gray  h4,.box-gray  i {
	margin-bottom: 20px;
}
.box-bottom {
	padding: 20px 0;
	text-align: center;
}
.box-bottom a {
	color: #fff;
	font-weight: 700;
}
.box-bottom a:hover {
	color: #eee;
	text-decoration: none;
}


/* Bottom
==================================== */

#bottom {
	background:#fcfcfc;
	padding:50px 0 0;

}
/* twitter */
#twitter-wrapper {
    text-align: center;
    width: 70%;
    margin: 0 auto;
}
#twitter em {
    font-style: normal;
    font-size: 13px;
}

#twitter em.twitterTime a {
	font-weight:600;
}

#twitter ul {
    padding: 0;
	list-style:none;
}
#twitter ul li {
    font-size: 20px;
    line-height: 1.6em;
    font-weight: 300;
    margin-bottom: 20px;
    position: relative;
    word-break: break-word;
}

.features .features-item {
  padding: 0 0 70px 0;
}
.features .features-item .features {
  margin-bottom: 34px;
}
.features .features-item .features .icon {
  float: left;
}
.features .features-item .features .icon i {
  z-index: 99;
  font-size: 26px;
  margin: 2px 8px 0 0;
  color: #ffc02a;
  background: #055999;
  padding: 16px;
  height: 58px;
  display: inline-block;
  border: 1px solid #055999;
}
.features .features-item .features-content {
  margin-left: 80px;
  padding-right: 68px;
}
.features .features-item .features-content h3 {
  padding-bottom: 8px;
  text-transform: uppercase;
  margin: 0;
  font-size: 18px;
}
.features .features-item .features:hover .icon-radius:after {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}
.features img{

margin: 10px 0 0 0;
}
/* page headline
==================================== */

#inner-headline{background: #2056ae url(../img/slides/1.jpg) top right;position: relative;margin: 0;padding: 24px 0;background-size: 39%;background-repeat: no-repeat;background-size: contain;padding: 0;}
#inner-headline:before{content:'';position: absolute;/* background: rgba(0, 0, 0, 0.42); */z-index: 1;top: 0;left: 0;right: 0;bottom: 0;}

#inner-headline h2.pageTitle{
	color: #ffffff;
	padding: 5px 0;
	text-align: left;
	display:block;
	font-size: 34px;
	font-weight: 700;
	position: relative;
	z-index: 3;
}

/* --- breadcrumbs --- */
#inner-headline ul.breadcrumb {
	margin:40px 0;
	float:left;
}

#inner-headline ul.breadcrumb li {
	margin-bottom:0;
	padding-bottom:0;
}
#inner-headline ul.breadcrumb li {
	font-size:13px;
	color:#fff;
}

#inner-headline ul.breadcrumb li i{
	color:#dedede;
}

#inner-headline ul.breadcrumb li a {
	color:#fff;
}

ul.breadcrumb li a:hover {
	text-decoration:none;
}
.fancybox-title-inside-wrap {
    padding: 3px 30px 6px;
    background: #2F2F2F;
    text-align: center; 
}
.fancybox-title-inside-wrap h4{
    font-size: 18px;	
}
.fancybox-nav span { 
    background-color: transparent;
}
/* Forms
============================= */

/* --- contact form  ---- */
form#contactform input[type="text"] {
  width: 100%;
  border: 1px solid #f5f5f5;
  min-height: 40px;
  padding-left:20px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;

}

form#contactform textarea {
border: 1px solid #f5f5f5;
  width: 100%;
  padding-left:20px;
  padding-top:10px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;

}

form#contactform .validation {
	font-size:11px;
}

#sendmessage {
	border:1px solid #e6e6e6;
	background:#f6f6f6;
	display:none;
	text-align:center;
	padding:15px 12px 15px 65px;
	margin:10px 0;
	font-weight:600;
	margin-bottom:30px;

}

#sendmessage.show,.show  {
	display:block;
}
 
form#commentform input[type="text"] {
  width: 100%;
  min-height: 40px;
  padding-left:20px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
	-webkit-border-radius: 2px 2px 2px 2px;
		-moz-border-radius: 2px 2px 2px 2px;
			border-radius: 2px 2px 2px 2px;

}

form#commentform textarea {
  width: 100%;
  padding-left:20px;
  padding-top:10px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
	-webkit-border-radius: 2px 2px 2px 2px;
		-moz-border-radius: 2px 2px 2px 2px;
			border-radius: 2px 2px 2px 2px;
}


/* --- search form --- */
.search{
	float:right;
	margin:35px 0 0;
	padding-bottom:0;
}

#inner-headline form.input-append {
	margin:0;
	padding:0;
}



/*  Portfolio
================================ */

.work-nav #filters {
	margin: 0;
	padding: 0;
	list-style: none;
}

.work-nav #filters li {
	margin: 0 10px 30px 0;
	padding: 0;
	float:left;
}

.work-nav #filters li a {
	color: #7F8289;
	font-size: 16px;
	display: block;	
}

.work-nav #filters li a:hover {

}

.work-nav #filters li a.selected {
	color: #DE5E60;
}

#thumbs {
	margin: 0;
	padding: 0;	
}

#thumbs li {
	list-style-type: none;
}
 

.item-thumbs a + img {
	width: 100%;	
}

.item-thumbs .hover-wrap {
	position: absolute;
	display: block;
	width: 100%;
	height: 100%;
	
	opacity: 0;
	filter: alpha(opacity=0);
	
	-webkit-transition: all 450ms ease-out 0s;	
	   -moz-transition: all 450ms ease-out 0s;
		 -o-transition: all 450ms ease-out 0s;
		    transition: all 450ms ease-out 0s;
		  
	-webkit-transform: rotateY(180deg) scale(0.5,0.5);
	   -moz-transform: rotateY(180deg) scale(0.5,0.5);
		-ms-transform: rotateY(180deg) scale(0.5,0.5);
		 -o-transform: rotateY(180deg) scale(0.5,0.5);
			transform: rotateY(180deg) scale(0.5,0.5);	
}

.item-thumbs:hover .hover-wrap,
.item-thumbs.active .hover-wrap {
	opacity: 1;
	filter: alpha(opacity=100);
	
	-webkit-transform: rotateY(0deg) scale(1,1);
	   -moz-transform: rotateY(0deg) scale(1,1);
		-ms-transform: rotateY(0deg) scale(1,1);
		 -o-transform: rotateY(0deg) scale(1,1);
		    transform: rotateY(0deg) scale(1,1);
}

.item-thumbs .hover-wrap .overlay-img {
	position: absolute;
	width: 100%;
	height: 100%;
	opacity: 0.80;
	filter: alpha(opacity=80);
	background: rgba(228, 137, 7, 0.64);
}

.item-thumbs .hover-wrap .overlay-img-thumb {
	position: absolute;
	border-radius: 60px;
	top: 50%;
	left: 50%;
	margin: -16px 0 0 -16px;
	color: #fff;
	font-size: 32px;
	line-height: 1em;
	opacity: 1;
	filter: alpha(opacity=100);
}
 
ul.portfolio-categ{
	margin:10px 0 30px 0;
	padding:0;
	float:left;
	list-style:none;
}

ul.portfolio-categ li{
margin: 0; 
float: left;
list-style: none;
font-size: 13px;
font-weight: 600;
border: 1px solid #D5D5D5;
margin-right: 15px;
}

ul.portfolio-categ li a{
	display:block;
	padding: 8px 20px;
	color:#353535;
}
ul.portfolio-categ li.active{

border: 1px solid #055999;

background-color: #055999;

color: #0F0F29 !important;
}
ul.portfolio-categ li.active a:hover, ul.portfolio-categ li a:hover,ul.portfolio-categ li a:focus,ul.portfolio-categ li a:active {
	text-decoration:none;
	outline:0; 
}
ul.portfolio-categ li.active a{
color: #fff;}
  #accordion-alt3 .panel-heading h4 {
font-size: 13px;
line-height: 28px;
}
.panel .panel-heading h4 {
font-weight: 400;
}
.panel-title {
margin-top: 0;
margin-bottom: 0;
font-size: 15px;
color: inherit;
}
.panel-group .panel {
margin-bottom: 0;
border-radius: 2px;
}
.panel {
margin-bottom: 18px;
background-color: #F4F4F4;
border: 1px solid transparent;
border-radius: 2px;
-webkit-box-shadow: 0 1px 1px rgba(0,0,0,0.05);
box-shadow: 0 1px 1px rgba(0,0,0,0.05);
}
#accordion-alt3 .panel-heading h4 a i {font-size: 13px;line-height: 18px;width: 18px;height: 18px;margin-right: 5px;color: #055999;text-align: center;border-radius: 50%;margin-left: 6px;}  
.progress.pb-sm {
height: 6px!important;
}
.progress {
box-shadow: inset 0 0 2px rgba(0,0,0,.1);
}
.progress {
overflow: hidden;
height: 18px;
margin-bottom: 18px;
background-color: #f5f5f5;
border-radius: 2px;
-webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}
.progress .progress-bar.progress-bar-red {
background: #ed5441;
} 
.progress .progress-bar.progress-bar-green {
background: #51d466;
}
.progress .progress-bar.progress-bar-lblue {
background: #32c8de;
}
/* --- portfolio detail --- */
.top-wrapper {
	margin-bottom:20px;
}
.info-blocks {
margin-bottom: 15px;
}
.info-blocks i.icon-info-blocks {float: left;color: #055999;font-size: 30px;min-width: 50px;margin-top: 6px;text-align: center;background: #FFFFFF;height: 64px;padding: 18px;border: 1px solid #055999;}
.info-blocks .info-blocks-in {
padding: 0 10px;
overflow: hidden;
}
.info-blocks .info-blocks-in h3 {
color: #555;
font-size: 20px;
line-height: 28px;
margin:0px;
}
.info-blocks .info-blocks-in p {
font-size: 14px;
}
  
blockquote {
	font-size:16px;
	font-weight:400;
	font-family:'Noto Serif', serif;
	font-style:italic;
	padding-left:0;
	color:#a2a2a2;
	line-height:1.6em;
	border:none;
}

blockquote cite 							{ display:block; font-size:12px; color:#666; margin-top:10px; }
blockquote cite:before 					{ content:"\2014 \0020"; }
blockquote cite a,
blockquote cite a:visited,
blockquote cite a:visited 				{ color:#555; }

/* --- pullquotes --- */

.pullquote-left {
	display:block;
	color:#a2a2a2;
	font-family:'Noto Serif', serif;
	font-size:14px;
	line-height:1.6em;
	padding-left:20px;
}

.pullquote-right {
	display:block;
	color:#a2a2a2;
	font-family:'Noto Serif', serif;
	font-size:14px;
	line-height:1.6em;
	padding-right:20px;
}

/* --- button --- */
.btn{text-align: center;background: #055999;color: #fff;border-radius: 0;border: none;padding: 8px 15px;border: 1px solid #055999;}
.btn-theme {
	color: #fff;
	background: transparent;
	border: 1px solid #FFFFFF;
	padding: 12px 30px;
	font-weight: bold;
	border-radius: 10px;
}
.btn-theme:hover {
	color: #eee;
}

/* --- list style --- */

ul.general {
	list-style:none;
	margin-left:0;
}

ul.link-list{
	margin:0;
	padding:0;
	list-style:none;
}

ul.link-list li{
	margin:0;
	padding:2px 0 2px 0;
	list-style:none;
}
footer{
background: #1B1B1B;
}
footer ul.link-list li a{
	color: #949494;
}
footer ul.link-list li a:hover {
	color:#eee;
}
/* --- Heading style --- */

h4.heading {
	font-weight:700;
}

.heading { margin-bottom: 30px; }

.heading {
	position: relative;
	
}


.widgetheading {
	width:100%;

	padding:0;
}

#bottom .widgetheading {
	position: relative;
	border-bottom: #e6e6e6 1px solid;
	padding-bottom: 9px;
}

aside .widgetheading {
	position: relative;
	border-bottom: #e9e9e9 1px solid;
	padding-bottom: 9px;
}

footer .widgetheading {
	position: relative;
}

footer .widget .social-network {
	position:relative;
}


#bottom .widget .widgetheading span, aside .widget .widgetheading span, footer .widget .widgetheading span {	
	position: absolute;
	width: 60px;
	height: 1px;
	bottom: -1px;
	right:0;

}
.box-area{padding: 0 0;/* padding-top: 0; *//* height: 125px; */float: left;text-align: center;padding: 0px 20px;width: 100%;}
/* --- Map --- */
.map{
	position:relative;
	margin-top:-50px;
	margin-bottom:40px;
}

.map iframe{
	width:100%;
	height:450px;
	border:none;
}

.map-grid iframe{
	width:100%;
	height:350px;
	border:none;
	margin:0 0 -5px 0;
	padding:0;
}

 
ul.team-detail{
	margin:-10px 0 0 0;
	padding:0;
	list-style:none;
}

ul.team-detail li{
	border-bottom:1px dotted #e9e9e9;
	margin:0 0 15px 0;
	padding:0 0 15px 0;
	list-style:none;
}

ul.team-detail li label {
	font-size:13px;
}

ul.team-detail li h4, ul.team-detail li label{
	margin-bottom:0;
}

ul.team-detail li ul.social-network {
	border:none;
	margin:0;
	padding:0;
}

ul.team-detail li ul.social-network li {
	border:none;	
	margin:0;
}
ul.team-detail li ul.social-network li i {
	margin:0;
}

 
.pricing-title{
	background:#fff;
	text-align:center;
	padding:10px 0 10px 0;
}

.pricing-title h3{
	font-weight:600;
	margin-bottom:0;
}

.pricing-offer{
	background: #fcfcfc;
	text-align: center;
	padding:40px 0 40px 0;
	font-size:18px;
	border-top:1px solid #e6e6e6;
	border-bottom:1px solid #e6e6e6;
}

.pricing-box.activeItem .pricing-offer{
	color:#fff;
}

.pricing-offer strong{
	font-size:78px;
	line-height:89px;
}

.pricing-offer sup{
	font-size:28px;
}

.pricing-container{
	background: #fff;
	text-align:center;
	font-size:14px;
}

.pricing-container strong{
color:#353535;
}

.pricing-container ul{
	list-style:none;
	padding:0;
	margin:0;
}

.pricing-container ul li{
	border-bottom: 1px solid #E6E6E6;
	list-style: none;
	padding: 15px 0 15px 0;
	margin: 0 0 0 0;
	color: #222;
}

.pricing-action{
	margin:0;
	background: #fcfcfc;
	text-align:center;
	padding:20px 0 30px 0;
}

.pricing-wrapp{
	margin:0 auto;
	width:100%;
	background:#fd0000;
}
 .pricing-box-item {
border: 1px solid #e6e6e6;
	
	position:relative;
	margin:0 0 20px 0;
	padding:0;
  -webkit-box-shadow: 0 2px 0 rgba(0,0,0,0.03);
  -moz-box-shadow: 0 2px 0 rgba(0,0,0,0.03);
  box-shadow: 0 2px 0 rgba(0,0,0,0.03);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.pricing-box-item .pricing-heading {
	text-align: center;
	padding:0px 0 0px 0;
	display:block;
}
.pricing-box-item.activeItem .pricing-heading {
	text-align: center;
	padding:0px 0 1px 0;
	border-bottom:none;
	display:block;
	color:#fff;
}
.pricing-box-item.activeItem .pricing-heading h3 {
	 
}

.pricing-box-item .pricing-heading h3 strong {
	font-size: 24px;
	font-weight: bold;
	letter-spacing:-1px;
	color: #055999;
}
.pricing-box-item .pricing-heading h3 {
	font-size:32px;
	font-weight:300;
	letter-spacing:-1px;
	margin-bottom: 0;
}

.pricing-box-item .pricing-terms {
	text-align: center;
	display: block;
	overflow: hidden;
	padding: 11px 0 5px; 
}

.pricing-box-item .pricing-terms  h6 {
	margin-top: 20px;
	color: #000;
	font-size: 22px;
	font-weight: normal;
	background: #ffffff;
	padding: 20px 0;
}

.pricing-box-item .icon .price-circled {
    margin: 10px 10px 10px 0;
    display: inline-block !important;
    text-align: center !important;
    color: #fff;
    width: 68px;
    height: 68px;
	padding:12px;
    font-size: 16px;
	font-weight:700;
    line-height: 68px;
    text-shadow:none;
    cursor: pointer;
    background-color: #888;
    border-radius: 64px;
    -moz-border-radius: 64px;
    -webkit-border-radius: 64px;
}

.pricing-box-item  .pricing-action{
	margin:0;
	text-align:center;
	padding: 30px 15px;
}
 .pricing-action a:hover {
    color: #fff;
}
/* ===== Widgets ===== */

/* --- flickr --- */
.widget .flickr_badge {
	width:100%;
}
.widget .flickr_badge img { margin: 0 9px 20px 0; }

footer .widget .flickr_badge {
    width: 100%;
}
footer .widget .flickr_badge img {
    margin: 0 9px 20px 0;
}

.flickr_badge img {
    width: 50px;
    height: 50px;
    float: left;
	margin: 0 9px 20px 0;
}
 
/* --- Recent post widget --- */

.recent-post{
	margin:20px 0 0 0;
	padding:0;
	line-height:18px;
}

.recent-post h5 a:hover {
	text-decoration:none;
}

.recent-post .text h5 a {
	color:#353535;
}

  
footer{
	padding:50px 0 0 0;
	color: #949494;
}

footer a {
	color:#fff;
}

footer a:hover {
	color:#eee;
}

footer h1, footer h2, footer h3, footer h4, footer h5, footer h6{
	color: #FFFFFF;
}

footer address {
	line-height:1.6em;
}

footer h5 a:hover, footer a:hover {
	text-decoration:none;
}

ul.social-network {
	list-style:none;
	margin:0;
}

ul.social-network li {
	display:inline;
	margin: 0 5px;
}

#sub-footer{
	text-shadow:none;
	color:#f5f5f5;
	padding:0;
	padding-top:30px;
	margin:20px 0 0 0;
	background: #191919;
}

#sub-footer p{
	margin:0;
	padding:0;
}

#sub-footer span{
	color:#f5f5f5;
}

.copyright {
	text-align:left;
	font-size:12px;
}

#sub-footer ul.social-network {
	float:right;
}

  

/* scroll to top */
.scrollup{
    position:fixed;
    width:32px;
    height:32px;
    bottom:0px;
    right:20px;
    background: #055999;
}

a.scrollup {
	outline:0;
	text-align: center;
}

a.scrollup:hover,a.scrollup:active,a.scrollup:focus {
	opacity:1;
	text-decoration:none;
}
a.scrollup i {
	margin-top: 10px;
	color: #fff;
}
a.scrollup i:hover {
	text-decoration:none;
}



 
.absolute{
	position:absolute;
}

.relative{
	position:relative;
}

.aligncenter{
	text-align:center;
}

.aligncenter span{
	margin-left:0;
}

.floatright {
	float:right;
}

.floatleft {
	float:left;
}

.floatnone {
	float:none;
}

.aligncenter {
	text-align:center;
}
 
img.pull-left, .align-left{
	float:left;
	margin:0 15px 15px 0;
}

.widget img.pull-left {
	float:left;
	margin:0 15px 15px 0;
}

img.pull-right, .align-right {
	float:right;
	margin:0 0 15px 15px;
}

article img.pull-left, article .align-left{
	float:left;
	margin:5px 15px 15px 0;
}

article img.pull-right, article .align-right{
	float:right;
	margin:5px 0 15px 15px;
}
 ============================= */

.clear-marginbot{
	margin-bottom:0;
}

.marginbot10{
	margin-bottom:10px;
}
.marginbot20{
	margin-bottom:20px;
}
.marginbot30{
	margin-bottom:30px;
}
.marginbot40{
	margin-bottom:40px;
}

.clear-margintop{
	margin-top:0;
}

.margintop10{
	margin-top:10px;
}

.margintop20{
	margin-top:20px;
}

.margintop30{
	margin-top:30px;
}

.margintop40{
	margin-top:40px;
}
header .nav .caret {
    border-bottom-color: #f5f5f5;
    border-top-color: #adadad;
}

/*  Media queries 
============================= */

@media (min-width: 768px) and (max-width: 979px) {

	a.detail{
		background:none;
		width:100%;
	}


	
	footer .widget form  input#appendedInputButton {
		  display: block;
		  width: 91%;
		  -webkit-border-radius: 4px 4px 4px 4px;
			 -moz-border-radius: 4px 4px 4px 4px;
				  border-radius: 4px 4px 4px 4px;
	}
	
	footer .widget form  .input-append .btn {
		  display: block;
		  width: 100%;
		  padding-right: 0;
		  padding-left: 0;
		  -webkit-box-sizing: border-box;
			 -moz-box-sizing: border-box;
				  box-sizing: border-box;
				  margin-top:10px;
	}

	ul.related-folio li{
		width:156px;
		margin:0 20px 0 0;
	}	
}

@media (max-width: 767px) {
.navbar-default .navbar-collapse{
border-color: none;/* background: #202021; */}
.navbar-default .navbar-toggle .icon-bar {background-color: #055999;border-radius: 0;}
.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {background-color: rgba(221, 221, 221, 0);/* margin-top: 12px; */}
.navbar-default .navbar-collapse, .navbar-default .navbar-form {
border-color: rgba(255, 255, 255, 0.58);
margin-top: 15px;
}

  body {
    padding-right: 0;
    padding-left: 0;
  }
	.navbar-brand { 
		border-bottom: none;
	}
	.navbar-header {
		border-bottom: none;
	}
	
	.navbar-nav {
		border-top: none;
		float: none;
		width: 100%;
	}
.navbar .nav > .active > a, .navbar .nav > .active > a:hover {/* background: none; */font-weight: 700;color: #FFFFFF;}
	header .navbar-nav > li {
padding-bottom: 2px;
padding-top: 3px;
}
	header .nav li .dropdown-menu  {
		margin-top: 0;
	}

	.dropdown-menu {
	  position: absolute;
	  top: 0;
	  left: 40px;
	  z-index: 1000;
	  display: none;
	  float: left;
	  min-width: 160px;
	  padding: 5px 0;
	  margin: 2px 0 0;
	  font-size: 13px;
	  list-style: none;
	  background-color: #fff;
	  background-clip: padding-box;
	  border: 1px solid #f5f5f5;
	  border: 1px solid rgba(0, 0, 0, .15);
	  border-radius: 0;
	  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
			  box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
	}
	

	
	li.active  {
		border: none;
		overflow: hidden;
}

	
	.box {
		border-bottom:1px solid #e9e9e9;
		padding-bottom:20px;
	}

	.flexslider .slide-caption {
		width: 90%; 
		padding: 2%; 
		position: absolute; 
		left: 0; 
		bottom: -40px; 
	}


	#inner-headline .breadcrumb {
		float:left;
		clear:both;
		width:100%;
	}

	.breadcrumb > li {
		font-size:13px;
	}

	
	ul.portfolio li article a i.icon-48{
		width:20px;
		height:20px;
		font-size:16px;
		line-height:20px;
	}


	.left-sidebar{
		border-right:none;
		padding:0 0 0 0;
		border-bottom: 1px dotted #e6e6e6;
		padding-bottom:10px;
		margin-bottom:40px;
	}
	
	.right-sidebar{
		margin-top:30px;
		border-left:none;
		padding:0 0 0 0;
	}
	
	
	footer .col-lg-1, footer .col-lg-2, footer .col-md-3, footer .col-lg-4, footer .col-lg-5, footer .col-lg-6, 
	footer .col-lg-7, footer .col-lg-8, footer .col-lg-9, footer .col-lg-10, footer .col-lg-11, footer .col-lg-12{
		margin-bottom:20px;
	}

	#sub-footer ul.social-network {
		float:left;
	}
	

	
  [class*="span"] {
		margin-bottom:20px;
  }

}
@media (min-width:768px){
	.item-thumbs {
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;
    cursor: pointer;
    width: 48%;
    float: left;
    margin: 1%;
}
}
@media (max-width: 480px) {
	.bottom-article a.pull-right {
		float:left;
		margin-top:20px;
	}

	.search{
		float:left;
	}

	.flexslider .flex-caption {
		display:none;
	}


	.cta-text {
		margin:0 auto;
		text-align:center;	
	}
	
	ul.portfolio li article a i{
		width:20px;
		height:20px;
		font-size:14px;
	}


}

 
    /*==========  Mobile First Method  ==========*/
 
    /* Extra Small Devices, Phones */ 
    @media only screen and (min-width : 480px) {

		.item-thumbs {
			position: relative;
			overflow: hidden;
			margin-bottom: 30px;
			cursor: pointer;
			width: 98%;
			float: left;
			margin: 1%;
		}
    }

    /* Small Devices, Tablets */
    @media only screen and (min-width : 768px) {
		.item-thumbs {
			position: relative;
			overflow: hidden;
			margin-bottom: 30px;
			cursor: pointer;
			width: 46%;
			float: left;
			margin: 1%;
		}
    }

    /* Medium Devices, Desktops */
    @media only screen and (min-width : 992px) {

		.item-thumbs {
			position: relative;
			overflow: hidden;
			margin-bottom: 30px;
			cursor: pointer;
			width: 23%;
			float: left;
			margin: 1%;
		}
    }

    /* Large Devices, Wide Screens */
    @media only screen and (min-width : 1200px) {

		.item-thumbs {
			position: relative;
			overflow: hidden;
			margin-bottom: 30px;
			cursor: pointer;
			width: 23%;
			float: left;
			margin: 1%;
		}
    }
	
	
 