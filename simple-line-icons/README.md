Simple line icons
====

[![Gitter](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/thesabbir/simple-line-icons?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)

Simple line icons with CSS, SAAS, LESS & Web-fonts files.

Preview & Docs
===
[https://thesabbir.github.io/simple-line-icons] (https://thesabbir.github.io/simple-line-icons)


Installation
====

via bower

```shell

bower install simple-line-icons --save

```
via npm

```shell

npm install simple-line-icons --save

```

Or, you can also clone or download this repository as zip.


Customizing LESS/SASS variables
====

###LESS:

```less
@simple-line-font-path        : "/path/to/font/files";
@simple-line-font-family      : "desired-name-font-family";
@simple-line-icon-prefix      : prefix-;
```

###SASS:

```sass
$simple-line-font-path        : "/path/to/font/files";
$simple-line-font-family      : "desired-name-font-family";
$simple-line-icon-prefix      : "prefix-";
```


Credits
===
[<PERSON>a](https://twitter.com/byjml) for creating this awesome webfont & [Ahmad Firoz](https://twitter.com/firoz_usf) for extending it further.

Contributors
====
[Check Here](https://github.com/thesabbir/simple-line-icons/graphs/contributors)!

License
====
You're free to use the webfont in a template/theme intended for sale on marketplaces like Themeforest.
CSS, SCSS & LESS files are under [MIT] (http://opensource.org/licenses/MIT) License.
