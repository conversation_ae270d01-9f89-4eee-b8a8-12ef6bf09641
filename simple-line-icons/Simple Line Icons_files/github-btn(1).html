
<!-- saved from url=(0093)https://ghbtns.com/github-btn.html?user=thesabbir&repo=simple-line-icons&type=star&count=true -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script src="simple-line-icons"></script><style type="text/css"></style></head><body><style>body{padding:0;margin:0;font:700 11px/14px 'Helvetica Neue',Helvetica,Arial,sans-serif;overflow:hidden}.github-btn{height:20px;overflow:hidden}.gh-btn,.gh-count,.gh-ico{float:left}.gh-btn,.gh-count{padding:2px 5px 2px 4px;color:#333;text-decoration:none;text-shadow:0 1px 0 #fff;white-space:nowrap;cursor:pointer;border-radius:3px}.gh-btn{background-color:#eee;background-image:-webkit-gradient(linear,left top,left bottom,color-stop(0,#fcfcfc),color-stop(100%,#eee));background-image:-webkit-linear-gradient(top,#fcfcfc 0,#eee 100%);background-image:-moz-linear-gradient(top,#fcfcfc 0,#eee 100%);background-image:-ms-linear-gradient(top,#fcfcfc 0,#eee 100%);background-image:-o-linear-gradient(top,#fcfcfc 0,#eee 100%);background-image:linear-gradient(to bottom,#fcfcfc 0,#eee 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#fcfcfc', endColorstr='#eeeeee', GradientType=0);background-repeat:no-repeat;border:1px solid #d5d5d5}.gh-btn:focus,.gh-btn:hover{text-decoration:none;background-color:#ddd;background-image:-webkit-gradient(linear,left top,left bottom,color-stop(0,#eee),color-stop(100%,#ddd));background-image:-webkit-linear-gradient(top,#eee 0,#ddd 100%);background-image:-moz-linear-gradient(top,#eee 0,#ddd 100%);background-image:-ms-linear-gradient(top,#eee 0,#ddd 100%);background-image:-o-linear-gradient(top,#eee 0,#ddd 100%);background-image:linear-gradient(to bottom,#eee 0,#ddd 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#dddddd', GradientType=0);border-color:#ccc}.gh-btn:active{background-image:none;background-color:#dcdcdc;border-color:#b5b5b5;box-shadow:inset 0 2px 4px rgba(0,0,0,.15)}.gh-ico{width:14px;height:14px;margin-right:4px;background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjQwcHgiIGhlaWdodD0iNDBweCIgdmlld0JveD0iMTIgMTIgNDAgNDAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMTIgMTIgNDAgNDAiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwYXRoIGZpbGw9IiMzMzMzMzMiIGQ9Ik0zMiAxMy40Yy0xMC41IDAtMTkgOC41LTE5IDE5YzAgOC40IDUuNSAxNS41IDEzIDE4YzEgMC4yIDEuMy0wLjQgMS4zLTAuOWMwLTAuNSAwLTEuNyAwLTMuMiBjLTUuMyAxLjEtNi40LTIuNi02LjQtMi42QzIwIDQxLjYgMTguOCA0MSAxOC44IDQxYy0xLjctMS4yIDAuMS0xLjEgMC4xLTEuMWMxLjkgMC4xIDIuOSAyIDIuOSAyYzEuNyAyLjkgNC41IDIuMSA1LjUgMS42IGMwLjItMS4yIDAuNy0yLjEgMS4yLTIuNmMtNC4yLTAuNS04LjctMi4xLTguNy05LjRjMC0yLjEgMC43LTMuNyAyLTUuMWMtMC4yLTAuNS0wLjgtMi40IDAuMi01YzAgMCAxLjYtMC41IDUuMiAyIGMxLjUtMC40IDMuMS0wLjcgNC44LTAuN2MxLjYgMCAzLjMgMC4yIDQuNyAwLjdjMy42LTIuNCA1LjItMiA1LjItMmMxIDIuNiAwLjQgNC42IDAuMiA1YzEuMiAxLjMgMiAzIDIgNS4xYzAgNy4zLTQuNSA4LjktOC43IDkuNCBjMC43IDAuNiAxLjMgMS43IDEuMyAzLjVjMCAyLjYgMCA0LjYgMCA1LjJjMCAwLjUgMC40IDEuMSAxLjMgMC45YzcuNS0yLjYgMTMtOS43IDEzLTE4LjFDNTEgMjEuOSA0Mi41IDEzLjQgMzIgMTMuNHoiLz48L3N2Zz4=);background-size:100% 100%;background-repeat:no-repeat}.gh-count{position:relative;display:none;margin-left:4px;background-color:#fafafa;border:1px solid #d4d4d4}.gh-count:focus,.gh-count:hover{color:#4183C4}.gh-count:after,.gh-count:before{content:'';position:absolute;display:inline-block;width:0;height:0;border-color:transparent;border-style:solid}.gh-count:before{top:50%;left:-3px;margin-top:-4px;border-width:4px 4px 4px 0;border-right-color:#fafafa}.gh-count:after{top:50%;left:-4px;z-index:-1;margin-top:-5px;border-width:5px 5px 5px 0;border-right-color:#d4d4d4}.github-btn-large{height:30px}.github-btn-large .gh-btn,.github-btn-large .gh-count{padding:3px 10px 3px 8px;font-size:16px;line-height:22px;border-radius:4px}.github-btn-large .gh-ico{width:20px;height:20px}.github-btn-large .gh-count{margin-left:6px}.github-btn-large .gh-count:before{left:-5px;margin-top:-6px;border-width:6px 6px 6px 0}.github-btn-large .gh-count:after{left:-6px;margin-top:-7px;border-width:7px 7px 7px 0}</style><span class="github-btn github-stargazers" id="github-btn"><a class="gh-btn" id="gh-btn" href="https://github.com/thesabbir/simple-line-icons/" target="_blank" aria-label="Star on GitHub"><span class="gh-ico" aria-hidden="true"></span> <span class="gh-text" id="gh-text">Star</span></a> <a class="gh-count" id="gh-count" href="https://github.com/thesabbir/simple-line-icons/stargazers" target="_blank" aria-label="154 stargazers on GitHub" style="display: block;">154</a></span><script>function addCommas(t){return String(t).replace(/(\d)(?=(\d{3})+$)/g,"$1,")}function jsonp(t,e){var r=document.createElement("script");r.src=t+"?callback="+(e?e:"callback"),head.insertBefore(r,head.firstChild)}function callback(t){switch(type){case"watch":"2"===v?(counter.innerHTML=addCommas(t.data.subscribers_count),counter.setAttribute("aria-label",counter.innerHTML+" watchers"+labelSuffix)):(counter.innerHTML=addCommas(t.data.stargazers_count),counter.setAttribute("aria-label",counter.innerHTML+" stargazers"+labelSuffix));break;case"star":counter.innerHTML=addCommas(t.data.stargazers_count),counter.setAttribute("aria-label",counter.innerHTML+" stargazers"+labelSuffix);break;case"fork":counter.innerHTML=addCommas(t.data.network_count),counter.setAttribute("aria-label",counter.innerHTML+" forks"+labelSuffix);break;case"follow":counter.innerHTML=addCommas(t.data.followers),counter.setAttribute("aria-label",counter.innerHTML+" followers"+labelSuffix)}"true"===count&&"undefined"!==counter.innerHTML&&(counter.style.display="block")}var params=function(){for(var t,e=[],r=window.location.href.slice(window.location.href.indexOf("?")+1).split("&"),a=0;a<r.length;a++)t=r[a].split("="),e.push(t[0]),e[t[0]]=t[1];return e}(),user=params.user,repo=params.repo,type=params.type,count=params.count,size=params.size,v=params.v,head=document.getElementsByTagName("head")[0],button=document.getElementById("gh-btn"),mainButton=document.getElementById("github-btn"),text=document.getElementById("gh-text"),counter=document.getElementById("gh-count"),labelSuffix=" on GitHub";switch(button.href="https://github.com/"+user+"/"+repo+"/",type){case"watch":"2"===v?(mainButton.className+=" github-watchers",text.innerHTML="Watch",counter.href="https://github.com/"+user+"/"+repo+"/watchers"):(mainButton.className+=" github-stargazers",text.innerHTML="Star",counter.href="https://github.com/"+user+"/"+repo+"/stargazers");break;case"star":mainButton.className+=" github-stargazers",text.innerHTML="Star",counter.href="https://github.com/"+user+"/"+repo+"/stargazers";break;case"fork":mainButton.className+=" github-forks",text.innerHTML="Fork",button.href="https://github.com/"+user+"/"+repo+"/fork",counter.href="https://github.com/"+user+"/"+repo+"/network";break;case"follow":mainButton.className+=" github-me",text.innerHTML="Follow @"+user,button.href="https://github.com/"+user,counter.href="https://github.com/"+user+"/followers"}button.setAttribute("aria-label",text.innerHTML+labelSuffix),"large"===size&&(mainButton.className+=" github-btn-large"),jsonp("follow"===type?"https://api.github.com/users/"+user:"https://api.github.com/repos/"+user+"/"+repo);</script></body></html>