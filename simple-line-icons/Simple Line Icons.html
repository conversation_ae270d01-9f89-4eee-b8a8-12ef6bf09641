
<!-- saved from url=(0045)http://thesabbir.github.io/simple-line-icons/ -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="description" content="Simple line icons">
    <meta name="keywords" content="simple, line, icons, icon pack, web icon">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="./Simple Line Icons_files/simple-line-icons.css">
    <link rel="stylesheet" href="./Simple Line Icons_files/bootstrap.css">
    <link rel="stylesheet" href="./Simple Line Icons_files/style.css">
    <title>Simple Line Icons</title>
<style type="text/css"></style></head>
<body>

<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="https://github.com/thesabbir/simple-line-icons">Simple Line Icons</a>
        </div>

        <div class="collapse navbar-collapse" id="bs-navbar-collapse">
            <ul class="nav navbar-nav navbar-right">
                <li><iframe src="./Simple Line Icons_files/github-btn.html" frameborder="0" scrolling="0" width="100px" height="20px"></iframe></li>
                <li><iframe src="./Simple Line Icons_files/github-btn(1).html" frameborder="0" scrolling="0" width="100px" height="20px"></iframe></li>
            </ul>
        </div>
    </div>
</nav>

<div class="container mt">
    <h1 class="text-center">Preview</h1>
    <p class="text-center">Click on the icons to get the icon class name</p>
    <div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-user icons"></i><span class="name">user</span> <code class="code-preview" style="display: none;">.icon-user</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-people icons"></i><span class="name">people</span> <code class="code-preview" style="display: none;">.icon-people</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-user-female icons"></i><span class="name">user-female</span> <code class="code-preview" style="display: none;">.icon-user-female</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-user-follow icons"></i><span class="name">user-follow</span> <code class="code-preview" style="display: none;">.icon-user-follow</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-user-following icons"></i><span class="name">user-following</span> <code class="code-preview" style="display: none;">.icon-user-following</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-user-unfollow icons"></i><span class="name">user-unfollow</span> <code class="code-preview" style="display: none;">.icon-user-unfollow</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-login icons"></i><span class="name">login</span> <code class="code-preview" style="display: none;">.icon-login</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-logout icons"></i><span class="name">logout</span> <code class="code-preview" style="display: none;">.icon-logout</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-emotsmile icons"></i><span class="name">emotsmile</span> <code class="code-preview" style="display: none;">.icon-emotsmile</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-phone icons"></i><span class="name">phone</span> <code class="code-preview" style="display: none;">.icon-phone</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-call-end icons"></i><span class="name">call-end</span> <code class="code-preview" style="display: none;">.icon-call-end</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-call-in icons"></i><span class="name">call-in</span> <code class="code-preview" style="display: none;">.icon-call-in</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-call-out icons"></i><span class="name">call-out</span> <code class="code-preview" style="display: none;">.icon-call-out</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-map icons"></i><span class="name">map</span> <code class="code-preview" style="display: none;">.icon-map</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-location-pin icons"></i><span class="name">location-pin</span> <code class="code-preview" style="display: none;">.icon-location-pin</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-direction icons"></i><span class="name">direction</span> <code class="code-preview" style="display: none;">.icon-direction</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-directions icons"></i><span class="name">directions</span> <code class="code-preview" style="display: none;">.icon-directions</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-compass icons"></i><span class="name">compass</span> <code class="code-preview" style="display: none;">.icon-compass</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-layers icons"></i><span class="name">layers</span> <code class="code-preview" style="display: none;">.icon-layers</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-menu icons"></i><span class="name">menu</span> <code class="code-preview" style="display: none;">.icon-menu</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-list icons"></i><span class="name">list</span> <code class="code-preview" style="display: none;">.icon-list</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-options-vertical icons"></i><span class="name">options-vertical</span> <code class="code-preview" style="display: none;">.icon-options-vertical</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-options icons"></i><span class="name">options</span> <code class="code-preview" style="display: none;">.icon-options</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-down icons"></i><span class="name">arrow-down</span> <code class="code-preview" style="display: none;">.icon-arrow-down</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-left icons"></i><span class="name">arrow-left</span> <code class="code-preview" style="display: none;">.icon-arrow-left</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-right icons"></i><span class="name">arrow-right</span> <code class="code-preview" style="display: none;">.icon-arrow-right</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-up icons"></i><span class="name">arrow-up</span> <code class="code-preview" style="display: none;">.icon-arrow-up</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-up-circle icons"></i><span class="name">arrow-up-circle</span> <code class="code-preview" style="display: none;">.icon-arrow-up-circle</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-left-circle icons"></i><span class="name">arrow-left-circle</span> <code class="code-preview" style="display: none;">.icon-arrow-left-circle</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-right-circle icons"></i><span class="name">arrow-right-circle</span> <code class="code-preview" style="display: none;">.icon-arrow-right-circle</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-arrow-down-circle icons"></i><span class="name">arrow-down-circle</span> <code class="code-preview" style="display: none;">.icon-arrow-down-circle</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-check icons"></i><span class="name">check</span> <code class="code-preview" style="display: none;">.icon-check</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-clock icons"></i><span class="name">clock</span> <code class="code-preview" style="display: none;">.icon-clock</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-plus icons"></i><span class="name">plus</span> <code class="code-preview" style="display: none;">.icon-plus</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-close icons"></i><span class="name">close</span> <code class="code-preview" style="display: none;">.icon-close</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-trophy icons"></i><span class="name">trophy</span> <code class="code-preview" style="display: none;">.icon-trophy</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-screen-smartphone icons"></i><span class="name">screen-smartphone</span> <code class="code-preview" style="display: none;">.icon-screen-smartphone</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-screen-desktop icons"></i><span class="name">screen-desktop</span> <code class="code-preview" style="display: none;">.icon-screen-desktop</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-plane icons"></i><span class="name">plane</span> <code class="code-preview" style="display: none;">.icon-plane</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-notebook icons"></i><span class="name">notebook</span> <code class="code-preview" style="display: none;">.icon-notebook</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-mustache icons"></i><span class="name">mustache</span> <code class="code-preview" style="display: none;">.icon-mustache</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-mouse icons"></i><span class="name">mouse</span> <code class="code-preview" style="display: none;">.icon-mouse</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-magnet icons"></i><span class="name">magnet</span> <code class="code-preview" style="display: none;">.icon-magnet</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-energy icons"></i><span class="name">energy</span> <code class="code-preview" style="display: none;">.icon-energy</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-disc icons"></i><span class="name">disc</span> <code class="code-preview" style="display: none;">.icon-disc</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-cursor icons"></i><span class="name">cursor</span> <code class="code-preview" style="display: none;">.icon-cursor</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-cursor-move icons"></i><span class="name">cursor-move</span> <code class="code-preview" style="display: none;">.icon-cursor-move</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-crop icons"></i><span class="name">crop</span> <code class="code-preview" style="display: none;">.icon-crop</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-chemistry icons"></i><span class="name">chemistry</span> <code class="code-preview" style="display: none;">.icon-chemistry</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-speedometer icons"></i><span class="name">speedometer</span> <code class="code-preview" style="display: none;">.icon-speedometer</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-shield icons"></i><span class="name">shield</span> <code class="code-preview" style="display: none;">.icon-shield</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-screen-tablet icons"></i><span class="name">screen-tablet</span> <code class="code-preview" style="display: none;">.icon-screen-tablet</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-magic-wand icons"></i><span class="name">magic-wand</span> <code class="code-preview" style="display: none;">.icon-magic-wand</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-hourglass icons"></i><span class="name">hourglass</span> <code class="code-preview" style="display: none;">.icon-hourglass</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-graduation icons"></i><span class="name">graduation</span> <code class="code-preview" style="display: none;">.icon-graduation</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-ghost icons"></i><span class="name">ghost</span> <code class="code-preview" style="display: none;">.icon-ghost</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-game-controller icons"></i><span class="name">game-controller</span> <code class="code-preview" style="display: none;">.icon-game-controller</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-fire icons"></i><span class="name">fire</span> <code class="code-preview" style="display: none;">.icon-fire</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-eyeglass icons"></i><span class="name">eyeglass</span> <code class="code-preview" style="display: none;">.icon-eyeglass</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-envelope-open icons"></i><span class="name">envelope-open</span> <code class="code-preview" style="display: none;">.icon-envelope-open</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-envolope-letter icons"></i><span class="name">envolope-letter</span> <code class="code-preview" style="display: none;">.icon-envolope-letter</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-bell icons"></i><span class="name">bell</span> <code class="code-preview" style="display: none;">.icon-bell</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-badge icons"></i><span class="name">badge</span> <code class="code-preview" style="display: none;">.icon-badge</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-anchor icons"></i><span class="name">anchor</span> <code class="code-preview" style="display: none;">.icon-anchor</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-wallet icons"></i><span class="name">wallet</span> <code class="code-preview" style="display: none;">.icon-wallet</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-vector icons"></i><span class="name">vector</span> <code class="code-preview" style="display: none;">.icon-vector</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-speech icons"></i><span class="name">speech</span> <code class="code-preview" style="display: none;">.icon-speech</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-puzzle icons"></i><span class="name">puzzle</span> <code class="code-preview" style="display: none;">.icon-puzzle</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-printer icons"></i><span class="name">printer</span> <code class="code-preview" style="display: none;">.icon-printer</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-present icons"></i><span class="name">present</span> <code class="code-preview" style="display: none;">.icon-present</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-playlist icons"></i><span class="name">playlist</span> <code class="code-preview" style="display: none;">.icon-playlist</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-pin icons"></i><span class="name">pin</span> <code class="code-preview" style="display: none;">.icon-pin</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-picture icons"></i><span class="name">picture</span> <code class="code-preview" style="display: none;">.icon-picture</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-handbag icons"></i><span class="name">handbag</span> <code class="code-preview" style="display: none;">.icon-handbag</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-globe-alt icons"></i><span class="name">globe-alt</span> <code class="code-preview" style="display: none;">.icon-globe-alt</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-globe icons"></i><span class="name">globe</span> <code class="code-preview" style="display: none;">.icon-globe</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-folder-alt icons"></i><span class="name">folder-alt</span> <code class="code-preview" style="display: none;">.icon-folder-alt</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-folder icons"></i><span class="name">folder</span> <code class="code-preview" style="display: none;">.icon-folder</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-film icons"></i><span class="name">film</span> <code class="code-preview" style="display: none;">.icon-film</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-feed icons"></i><span class="name">feed</span> <code class="code-preview" style="display: none;">.icon-feed</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-drop icons"></i><span class="name">drop</span> <code class="code-preview" style="display: none;">.icon-drop</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-drawar icons"></i><span class="name">drawar</span> <code class="code-preview" style="display: none;">.icon-drawar</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-docs icons"></i><span class="name">docs</span> <code class="code-preview" style="display: none;">.icon-docs</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-doc icons"></i><span class="name">doc</span> <code class="code-preview" style="display: none;">.icon-doc</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-diamond icons"></i><span class="name">diamond</span> <code class="code-preview" style="display: none;">.icon-diamond</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-cup icons"></i><span class="name">cup</span> <code class="code-preview" style="display: none;">.icon-cup</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-calculator icons"></i><span class="name">calculator</span> <code class="code-preview" style="display: none;">.icon-calculator</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-bubbles icons"></i><span class="name">bubbles</span> <code class="code-preview" style="display: none;">.icon-bubbles</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-briefcase icons"></i><span class="name">briefcase</span> <code class="code-preview" style="display: none;">.icon-briefcase</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-book-open icons"></i><span class="name">book-open</span> <code class="code-preview" style="display: none;">.icon-book-open</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-basket-loaded icons"></i><span class="name">basket-loaded</span> <code class="code-preview" style="display: none;">.icon-basket-loaded</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-basket icons"></i><span class="name">basket</span> <code class="code-preview" style="display: none;">.icon-basket</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-bag icons"></i><span class="name">bag</span> <code class="code-preview" style="display: none;">.icon-bag</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-action-undo icons"></i><span class="name">action-undo</span> <code class="code-preview" style="display: none;">.icon-action-undo</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-action-redo icons"></i><span class="name">action-redo</span> <code class="code-preview" style="display: none;">.icon-action-redo</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-wrench icons"></i><span class="name">wrench</span> <code class="code-preview" style="display: none;">.icon-wrench</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-umbrella icons"></i><span class="name">umbrella</span> <code class="code-preview" style="display: none;">.icon-umbrella</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-trash icons"></i><span class="name">trash</span> <code class="code-preview" style="display: none;">.icon-trash</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-tag icons"></i><span class="name">tag</span> <code class="code-preview" style="display: none;">.icon-tag</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-support icons"></i><span class="name">support</span> <code class="code-preview" style="display: none;">.icon-support</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-frame icons"></i><span class="name">frame</span> <code class="code-preview" style="display: none;">.icon-frame</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-size-fullscreen icons"></i><span class="name">size-fullscreen</span> <code class="code-preview" style="display: none;">.icon-size-fullscreen</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-size-actual icons"></i><span class="name">size-actual</span> <code class="code-preview" style="display: none;">.icon-size-actual</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-shuffle icons"></i><span class="name">shuffle</span> <code class="code-preview" style="display: none;">.icon-shuffle</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-share-alt icons"></i><span class="name">share-alt</span> <code class="code-preview" style="display: none;">.icon-share-alt</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-share icons"></i><span class="name">share</span> <code class="code-preview" style="display: none;">.icon-share</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-rocket icons"></i><span class="name">rocket</span> <code class="code-preview" style="display: none;">.icon-rocket</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-question icons"></i><span class="name">question</span> <code class="code-preview" style="display: none;">.icon-question</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-pie-chart icons"></i><span class="name">pie-chart</span> <code class="code-preview" style="display: none;">.icon-pie-chart</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-pencil icons"></i><span class="name">pencil</span> <code class="code-preview" style="display: none;">.icon-pencil</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-note icons"></i><span class="name">note</span> <code class="code-preview" style="display: none;">.icon-note</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-loop icons"></i><span class="name">loop</span> <code class="code-preview" style="display: none;">.icon-loop</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-home icons"></i><span class="name">home</span> <code class="code-preview" style="display: none;">.icon-home</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-grid icons"></i><span class="name">grid</span> <code class="code-preview" style="display: none;">.icon-grid</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-graph icons"></i><span class="name">graph</span> <code class="code-preview" style="display: none;">.icon-graph</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-microphone icons"></i><span class="name">microphone</span> <code class="code-preview" style="display: none;">.icon-microphone</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-music-tone-alt icons"></i><span class="name">music-tone-alt</span> <code class="code-preview" style="display: none;">.icon-music-tone-alt</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-music-tone icons"></i><span class="name">music-tone</span> <code class="code-preview" style="display: none;">.icon-music-tone</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-earphones-alt icons"></i><span class="name">earphones-alt</span> <code class="code-preview" style="display: none;">.icon-earphones-alt</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-earphones icons"></i><span class="name">earphones</span> <code class="code-preview" style="display: none;">.icon-earphones</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-equalizer icons"></i><span class="name">equalizer</span> <code class="code-preview" style="display: none;">.icon-equalizer</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-like icons"></i><span class="name">like</span> <code class="code-preview" style="display: none;">.icon-like</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-dislike icons"></i><span class="name">dislike</span> <code class="code-preview" style="display: none;">.icon-dislike</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-control-start icons"></i><span class="name">control-start</span> <code class="code-preview" style="display: none;">.icon-control-start</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-control-rewind icons"></i><span class="name">control-rewind</span> <code class="code-preview" style="display: none;">.icon-control-rewind</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-control-play icons"></i><span class="name">control-play</span> <code class="code-preview" style="display: none;">.icon-control-play</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-control-pause icons"></i><span class="name">control-pause</span> <code class="code-preview" style="display: none;">.icon-control-pause</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-control-forward icons"></i><span class="name">control-forward</span> <code class="code-preview" style="display: none;">.icon-control-forward</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-control-end icons"></i><span class="name">control-end</span> <code class="code-preview" style="display: none;">.icon-control-end</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-volume-1 icons"></i><span class="name">volume-1</span> <code class="code-preview" style="display: none;">.icon-volume-1</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-volume-2 icons"></i><span class="name">volume-2</span> <code class="code-preview" style="display: none;">.icon-volume-2</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-volume-off icons"></i><span class="name">volume-off</span> <code class="code-preview" style="display: none;">.icon-volume-off</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-calender icons"></i><span class="name">calender</span> <code class="code-preview" style="display: none;">.icon-calender</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-bulb icons"></i><span class="name">bulb</span> <code class="code-preview" style="display: none;">.icon-bulb</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-chart icons"></i><span class="name">chart</span> <code class="code-preview" style="display: none;">.icon-chart</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-ban icons"></i><span class="name">ban</span> <code class="code-preview" style="display: none;">.icon-ban</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-bubble icons"></i><span class="name">bubble</span> <code class="code-preview" style="display: none;">.icon-bubble</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-camrecorder icons"></i><span class="name">camrecorder</span> <code class="code-preview" style="display: none;">.icon-camrecorder</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-camera icons"></i><span class="name">camera</span> <code class="code-preview" style="display: none;">.icon-camera</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-cloud-download icons"></i><span class="name">cloud-download</span> <code class="code-preview" style="display: none;">.icon-cloud-download</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-cloud-upload icons"></i><span class="name">cloud-upload</span> <code class="code-preview" style="display: none;">.icon-cloud-upload</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-envolope icons"></i><span class="name">envolope</span> <code class="code-preview" style="display: none;">.icon-envolope</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-eye icons"></i><span class="name">eye</span> <code class="code-preview" style="display: none;">.icon-eye</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-flag icons"></i><span class="name">flag</span> <code class="code-preview" style="display: none;">.icon-flag</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-heart icons"></i><span class="name">heart</span> <code class="code-preview" style="display: none;">.icon-heart</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-info icons"></i><span class="name">info</span> <code class="code-preview" style="display: none;">.icon-info</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-key icons"></i><span class="name">key</span> <code class="code-preview" style="display: none;">.icon-key</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-link icons"></i><span class="name">link</span> <code class="code-preview" style="display: none;">.icon-link</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-lock icons"></i><span class="name">lock</span> <code class="code-preview" style="display: none;">.icon-lock</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-lock-open icons"></i><span class="name">lock-open</span> <code class="code-preview" style="display: none;">.icon-lock-open</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-magnifier icons"></i><span class="name">magnifier</span> <code class="code-preview" style="display: none;">.icon-magnifier</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-magnifier-add icons"></i><span class="name">magnifier-add</span> <code class="code-preview" style="display: none;">.icon-magnifier-add</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-magnifier-remove icons"></i><span class="name">magnifier-remove</span> <code class="code-preview" style="display: none;">.icon-magnifier-remove</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-paper-clip icons"></i><span class="name">paper-clip</span> <code class="code-preview" style="display: none;">.icon-paper-clip</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-paper-plane icons"></i><span class="name">paper-plane</span> <code class="code-preview" style="display: none;">.icon-paper-plane</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-power icons"></i><span class="name">power</span> <code class="code-preview" style="display: none;">.icon-power</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-refresh icons"></i><span class="name">refresh</span> <code class="code-preview" style="display: none;">.icon-refresh</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-reload icons"></i><span class="name">reload</span> <code class="code-preview" style="display: none;">.icon-reload</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-settings icons"></i><span class="name">settings</span> <code class="code-preview" style="display: none;">.icon-settings</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-star icons"></i><span class="name">star</span> <code class="code-preview" style="display: none;">.icon-star</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-symble-female icons"></i><span class="name">symble-female</span> <code class="code-preview" style="display: none;">.icon-symble-female</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-symbol-male icons"></i><span class="name">symbol-male</span> <code class="code-preview" style="display: none;">.icon-symbol-male</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-target icons"></i><span class="name">target</span> <code class="code-preview" style="display: none;">.icon-target</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-credit-card icons"></i><span class="name">credit-card</span> <code class="code-preview" style="display: none;">.icon-credit-card</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-paypal icons"></i><span class="name">paypal</span> <code class="code-preview" style="display: none;">.icon-paypal</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-tumblr icons"></i><span class="name">social-tumblr</span> <code class="code-preview" style="display: none;">.icon-social-tumblr</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-twitter icons"></i><span class="name">social-twitter</span> <code class="code-preview" style="display: none;">.icon-social-twitter</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-facebook icons"></i><span class="name">social-facebook</span> <code class="code-preview" style="display: none;">.icon-social-facebook</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-instagram icons"></i><span class="name">social-instagram</span> <code class="code-preview" style="display: none;">.icon-social-instagram</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-linkedin icons"></i><span class="name">social-linkedin</span> <code class="code-preview" style="display: none;">.icon-social-linkedin</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-pintarest icons"></i><span class="name">social-pintarest</span> <code class="code-preview" style="display: none;">.icon-social-pintarest</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-github icons"></i><span class="name">social-github</span> <code class="code-preview" style="display: none;">.icon-social-github</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-gplus icons"></i><span class="name">social-gplus</span> <code class="code-preview" style="display: none;">.icon-social-gplus</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-reddit icons"></i><span class="name">social-reddit</span> <code class="code-preview" style="display: none;">.icon-social-reddit</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-skype icons"></i><span class="name">social-skype</span> <code class="code-preview" style="display: none;">.icon-social-skype</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-dribbble icons"></i><span class="name">social-dribbble</span> <code class="code-preview" style="display: none;">.icon-social-dribbble</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-behance icons"></i><span class="name">social-behance</span> <code class="code-preview" style="display: none;">.icon-social-behance</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-foursqare icons"></i><span class="name">social-foursqare</span> <code class="code-preview" style="display: none;">.icon-social-foursqare</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-soundcloud icons"></i><span class="name">social-soundcloud</span> <code class="code-preview" style="display: none;">.icon-social-soundcloud</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-spotify icons"></i><span class="name">social-spotify</span> <code class="code-preview" style="display: none;">.icon-social-spotify</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-stumbleupon icons"></i><span class="name">social-stumbleupon</span> <code class="code-preview" style="display: none;">.icon-social-stumbleupon</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-youtube icons"></i><span class="name">social-youtube</span> <code class="code-preview" style="display: none;">.icon-social-youtube</code></a>
                </div>
            </div><div class="icon-preview-box col-xs-6 col-md-3 col-lg-3">
                <div class="preview">
                <a href="http://thesabbir.github.io/simple-line-icons/#" class="show-code" title="click to show css class name"><i class="icon-social-dropbox icons"></i><span class="name">social-dropbox</span> <code class="code-preview" style="display: none;">.icon-social-dropbox</code></a>
                </div>
            </div>
</div>
<footer class="footer">
    <div class="container">
        <p class="pull-left">Brought to you by <a href="https://twitter.com/alreadysabbir">Sabbir</a> &amp; <a href="https://github.com/thesabbir/simple-line-icons#contributors">Contributors</a></p>
        <p class="pull-right"><a href="https://github.com/thesabbir/simple-line-icons">Contribute!</a></p>
    </div>
</footer>
<script src="./Simple Line Icons_files/jquery.min.js"></script>
<script src="./Simple Line Icons_files/bootstrap.min.js"></script>
<script src="./Simple Line Icons_files/app.js"></script>


</body></html>